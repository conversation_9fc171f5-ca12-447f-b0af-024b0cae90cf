#!/usr/bin/env python3
"""
测试VLLM服务连接的脚本
"""
import sys
import os
import json
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import settings, ModelConfig
from app.services.llm_service import llm_service


def test_direct_api_call():
    """直接测试VLLM API调用"""
    print("=== 直接API调用测试 ===")
    
    try:
        url = f"{settings.llm_service_url}/v1/completions"
        data = {
            "model": settings.vllm_model_path,
            "prompt": "你好，请简单介绍一下法律咨询的重要性",
            "max_tokens": 100,
            "temperature": 0.1
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            url,
            headers={"Content-Type": "application/json"},
            json=data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应成功!")
            print(f"生成的文本: {result.get('choices', [{}])[0].get('text', '无内容')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"直接API调用失败: {e}")


def test_llm_service():
    """测试LLM服务封装"""
    print("\n=== LLM服务封装测试 ===")
    
    try:
        result = llm_service.test_connection()
        print(f"测试结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result["status"] == "success":
            print("✅ LLM服务连接成功!")
        else:
            print("❌ LLM服务连接失败!")
            
    except Exception as e:
        print(f"LLM服务测试失败: {e}")


def test_legal_qa():
    """测试法律问答功能"""
    print("\n=== 法律问答功能测试 ===")
    
    try:
        question = "什么是合同法？"
        context_documents = [
            {
                "title": "中华人民共和国合同法",
                "content": "合同法是调整平等主体的自然人、法人、其他组织之间设立、变更、终止民事权利义务关系的协议的法律规范。",
                "source": "法律条文"
            }
        ]
        
        answer = llm_service.generate_legal_answer(question, context_documents)
        print(f"问题: {question}")
        print(f"回答: {answer}")
        
    except Exception as e:
        print(f"法律问答测试失败: {e}")


def main():
    """主函数"""
    print("VLLM服务连接测试")
    print("=" * 50)
    
    # 显示配置信息
    print(f"服务URL: {settings.llm_service_url}")
    print(f"模型路径: {settings.vllm_model_path}")
    print(f"模型名称: {settings.vllm_model_name}")
    print("=" * 50)
    
    # 运行测试
    test_direct_api_call()
    test_llm_service()
    test_legal_qa()
    
    print("\n测试完成!")


if __name__ == "__main__":
    main()
